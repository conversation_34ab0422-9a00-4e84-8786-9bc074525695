import React from 'react'

const Pricing = () => {
  const features = [
    'Acesso a todos os agentes',
    'Documentos ilimitados',
    'Formatos Word, Excel e PDF',
    'Memória por cliente',
    'Suporte por email',
    'Atualizações automáticas'
  ]

  return (
    <section id="preco" className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-black mb-4">
            Preço simples
          </h2>
          <p className="text-xl text-gray-600">
            Um plano único que inclui tudo que você precisa
          </p>
        </div>

        <div className="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden">
          <div className="bg-blue-600 text-white text-center py-4">
            <span className="text-sm font-medium">PLANO ÚNICO</span>
          </div>

          <div className="p-8 lg:p-12">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <span className="text-5xl font-bold text-black">R$ 9,99</span>
                <span className="text-xl text-gray-500 ml-2">/mês</span>
              </div>
              <p className="text-gray-600">
                Por usuário, sem fidelidade
              </p>
            </div>

            <div className="space-y-4 mb-8">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
            </div>

            <div className="text-center">
              <button className="btn-primary w-full text-lg py-4 mb-4">
                Assinar agora
              </button>
              <p className="text-sm text-gray-500">
                Sem fidelidade. Cancele quando quiser.
              </p>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-black text-center mb-8">
            Perguntas frequentes
          </h3>

          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <h4 className="font-semibold text-black mb-2">
                Posso cancelar a qualquer momento?
              </h4>
              <p className="text-gray-600">
                Sim, não há fidelidade. Você pode cancelar sua assinatura a qualquer momento e continuar usando até o final do período pago.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <h4 className="font-semibold text-black mb-2">
                Há limite de documentos?
              </h4>
              <p className="text-gray-600">
                Não, você pode gerar quantos documentos precisar. O único limite é o bom senso para não sobrecarregar nossos servidores.
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <h4 className="font-semibold text-black mb-2">
                Os documentos ficam salvos?
              </h4>
              <p className="text-gray-600">
                Sim, todos os documentos gerados ficam salvos na sua conta por 12 meses. Você pode baixá-los novamente quando quiser.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Pricing
