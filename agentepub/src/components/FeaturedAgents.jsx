import React from 'react'

const FeaturedAgents = () => {
  const agents = [
    {
      title: 'Brief Doctor',
      description: 'Transforma briefings ruins em documentos acionáveis com objetivos claros e métricas definidas.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      color: 'bg-blue-500',
      example: 'Ver exemplo'
    },
    {
      title: 'Social Grid',
      description: 'Gera calendário semanal de posts com copy, hashtags e sugestões visuais para redes sociais.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      color: 'bg-green-500',
      example: 'Ver exemplo'
    },
    {
      title: 'Mídia Mix',
      description: 'Sugere mix de canais e distribuição de orçamento baseado no público-alvo e objetivos.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      color: 'bg-purple-500',
      example: 'Ver exemplo'
    }
  ]

  return (
    <section id="agentes" className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-black mb-4">
            Agentes em destaque
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Cada agente é especializado em uma área específica do trabalho publicitário
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {agents.map((agent, index) => (
            <div 
              key={index} 
              className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100"
            >
              <div className="flex items-center mb-6">
                <div className={`w-16 h-16 ${agent.color} rounded-xl flex items-center justify-center text-white mr-4`}>
                  {agent.icon}
                </div>
              </div>
              
              <h3 className="text-xl font-semibold text-black mb-3">
                {agent.title}
              </h3>
              
              <p className="text-gray-600 leading-relaxed mb-6">
                {agent.description}
              </p>
              
              <button className="btn-secondary w-full">
                {agent.example}
              </button>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-600 mb-6">
            Mais de 15 agentes especializados disponíveis na plataforma
          </p>
          <button className="btn-primary text-lg px-8 py-4">
            Ver todos os agentes
          </button>
        </div>
      </div>
    </section>
  )
}

export default FeaturedAgents
