import React, { useState } from 'react'

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <nav className="fixed top-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-b border-gray-100 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <h1 className="text-2xl font-bold text-black">AgentePub</h1>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-8">
              <a href="#como-funciona" className="text-gray-700 hover:text-black transition-colors duration-200">
                Como funciona
              </a>
              <a href="#agentes" className="text-gray-700 hover:text-black transition-colors duration-200">
                Agentes
              </a>
              <a href="#preco" className="text-gray-700 hover:text-black transition-colors duration-200">
                Preço
              </a>
              <a href="#entrar" className="text-gray-700 hover:text-black transition-colors duration-200">
                Entrar
              </a>
            </div>
          </div>

          {/* CTA Button */}
          <div className="hidden md:block">
            <button className="btn-primary">
              Testar agora
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-black focus:outline-none focus:text-black"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-100">
              <a href="#como-funciona" className="block px-3 py-2 text-gray-700 hover:text-black">
                Como funciona
              </a>
              <a href="#agentes" className="block px-3 py-2 text-gray-700 hover:text-black">
                Agentes
              </a>
              <a href="#preco" className="block px-3 py-2 text-gray-700 hover:text-black">
                Preço
              </a>
              <a href="#entrar" className="block px-3 py-2 text-gray-700 hover:text-black">
                Entrar
              </a>
              <div className="px-3 py-2">
                <button className="btn-primary w-full">
                  Testar agora
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

export default Navbar
