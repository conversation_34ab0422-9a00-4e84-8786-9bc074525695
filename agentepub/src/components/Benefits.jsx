import React from 'react'

const Benefits = () => {
  const benefits = [
    {
      title: 'Consistência nas entregas',
      description: 'Todos os documentos seguem o mesmo padrão profissional, garantindo qualidade uniforme.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      title: 'Velocidade real',
      description: 'O que levava horas para ser produzido agora fica pronto em minutos.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      )
    },
    {
      title: 'Formatos prontos',
      description: 'Documentos em Word, planilhas Excel e PDFs prontos para apresentação.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      )
    },
    {
      title: 'Memória por cliente',
      description: 'A plataforma lembra do histórico e preferências de cada cliente automaticamente.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      )
    }
  ]

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-black mb-4">
            Por que escolher AgentePub?
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Benefícios que fazem a diferença no dia a dia da sua agência
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => (
            <div 
              key={index} 
              className="bg-white rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300 text-center"
            >
              <div className="w-16 h-16 bg-primary-100 text-primary-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                {benefit.icon}
              </div>
              
              <h3 className="text-lg font-semibold text-black mb-3">
                {benefit.title}
              </h3>
              
              <p className="text-gray-600 leading-relaxed text-sm">
                {benefit.description}
              </p>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-16 bg-white rounded-2xl p-8 shadow-sm">
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-primary-600 mb-2">85%</div>
              <p className="text-gray-600">Redução no tempo de produção</p>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary-600 mb-2">200+</div>
              <p className="text-gray-600">Agências já utilizam</p>
            </div>
            <div>
              <div className="text-3xl font-bold text-primary-600 mb-2">4.8/5</div>
              <p className="text-gray-600">Avaliação dos usuários</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Benefits
