{"name": "@tailwindcss/vite", "version": "4.1.12", "description": "A utility-first CSS framework for rapidly building custom user interfaces.", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tailwindlabs/tailwindcss.git", "directory": "packages/@tailwindcss-vite"}, "bugs": "https://github.com/tailwindlabs/tailwindcss/issues", "homepage": "https://tailwindcss.com", "files": ["dist/"], "publishConfig": {"provenance": true, "access": "public"}, "exports": {".": {"types": "./dist/index.d.mts", "import": "./dist/index.mjs"}}, "dependencies": {"tailwindcss": "4.1.12", "@tailwindcss/node": "4.1.12", "@tailwindcss/oxide": "4.1.12"}, "devDependencies": {"@types/node": "^20.19.0", "vite": "^7.0.0"}, "peerDependencies": {"vite": "^5.2.0 || ^6 || ^7"}, "scripts": {"build": "tsup-node", "dev": "pnpm run build -- --watch"}}